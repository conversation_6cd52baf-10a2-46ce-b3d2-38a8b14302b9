#!/usr/bin/env python3
"""
Install FastMCP Demo Server into Claude Code

This script helps you install the FastMCP demo server into Claude Code
using the FastMCP CLI installation feature.
"""

import subprocess
import sys
import json
from pathlib import Path


def install_to_claude_code():
    """Install the server to Claude Code using FastMCP CLI."""
    print("🔗 Installing FastMCP Demo Server to Claude Code...")
    
    try:
        # Use FastMCP CLI to install
        result = subprocess.run([
            "fastmcp", "install", "claude-code", "server.py",
            "--name", "fastmcp-demo-server"
        ], capture_output=True, text=True, cwd=Path.cwd())
        
        if result.returncode == 0:
            print("✅ Successfully installed to Claude Code!")
            print(result.stdout)
        else:
            print("❌ Installation failed:")
            print(result.stderr)
            return False
            
    except FileNotFoundError:
        print("❌ FastMCP CLI not found. Make sure FastMCP is installed:")
        print("   pip install fastmcp")
        return False
    except Exception as e:
        print(f"❌ Error during installation: {e}")
        return False
    
    return True


def create_manual_config():
    """Create manual configuration for Claude Code."""
    print("\n📝 Creating manual configuration...")
    
    config = {
        "mcpServers": {
            "fastmcp-demo-server": {
                "command": "python",
                "args": [str(Path.cwd() / "server.py")],
                "env": {
                    "FASTMCP_LOG_LEVEL": "INFO"
                }
            }
        }
    }
    
    # Save configuration
    config_file = Path("claude_code_config.json")
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Manual configuration saved to: {config_file}")
    print("\n📋 Manual Installation Steps:")
    print("1. Open Claude Code")
    print("2. Go to Settings (Cmd/Ctrl + ,)")
    print("3. Search for 'MCP' or go to Extensions > MCP")
    print("4. Add the following configuration:")
    print(f"\n{json.dumps(config, indent=2)}")
    
    return config_file


def main():
    """Main installation function."""
    print("🚀 FastMCP Demo Server - Claude Code Integration")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("server.py").exists():
        print("❌ server.py not found in current directory")
        print("Please run this script from the FastMCP demo server directory")
        sys.exit(1)
    
    print("Choose installation method:")
    print("1. Automatic installation using FastMCP CLI (recommended)")
    print("2. Generate manual configuration")
    
    choice = input("\nEnter your choice (1 or 2): ").strip()
    
    if choice == "1":
        success = install_to_claude_code()
        if not success:
            print("\n🔄 Falling back to manual configuration...")
            create_manual_config()
    elif choice == "2":
        create_manual_config()
    else:
        print("❌ Invalid choice. Please run the script again.")
        sys.exit(1)
    
    print("\n🎉 Integration setup complete!")
    print("\n📚 Next Steps:")
    print("1. Restart Claude Code if it was running")
    print("2. Open a new chat or project")
    print("3. Your FastMCP demo server should be available")
    print("4. Try asking Claude to use tools like 'add two numbers' or 'generate sample data'")


if __name__ == "__main__":
    main()
