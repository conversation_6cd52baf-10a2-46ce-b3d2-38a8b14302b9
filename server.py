#!/usr/bin/env python3
"""
FastMCP 2.0 Demo Server

A comprehensive demonstration of FastMCP 2.0 capabilities including:
- Tools for calculations and data processing
- Resources for configuration and data access
- Resource templates for dynamic content
- Enhanced error handling and logging
- Async operations and context usage
- Input validation and safety checks
"""

import asyncio
import json
import math
import os
import random
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Annotated, Any, Dict, List, Literal, Optional

import aiofiles
import aiohttp
from pydantic import BaseModel, Field

from fastmcp import FastMCP, Context
from fastmcp.exceptions import ToolError

# Import our custom modules
from logging_config import setup_logging, log_server_startup, log_tool_execution
from error_handling import (
    handle_tool_errors,
    handle_async_tool_errors,
    validate_numeric_range,
    validate_string_length,
    validate_file_path,
    ValidationError,
    FileOperationError,
    NetworkError,
    DataProcessingError
)


# Pydantic models for structured data
class UserProfile(BaseModel):
    """User profile data structure"""
    id: int
    name: str
    email: str
    created_at: datetime
    is_active: bool = True
    tags: List[str] = []


class CalculationResult(BaseModel):
    """Result of a mathematical calculation"""
    operation: str
    inputs: List[float]
    result: float
    timestamp: datetime


class WeatherData(BaseModel):
    """Weather information structure"""
    location: str
    temperature: float
    humidity: int
    description: str
    timestamp: datetime


# Set up logging
logger = setup_logging(level="INFO", log_file="logs/fastmcp_demo.log")

# Create the FastMCP server instance
mcp = FastMCP(
    name="FastMCP Demo Server",
    instructions="""
    This is a demonstration server showcasing FastMCP 2.0 capabilities.

    Available tools:
    - Mathematical calculations (add, multiply, advanced_math)
    - Text processing (reverse_text, word_count)
    - Data generation (generate_data, create_user)
    - File operations (read_file, write_file)
    - Network operations (fetch_url)
    - System information (get_system_info)

    Available resources:
    - Server configuration and status
    - Dynamic user profiles
    - Sample data sets
    """
)


# =============================================================================
# TOOLS - Functions that LLMs can execute
# =============================================================================

@mcp.tool
@handle_tool_errors
def add(a: float, b: float) -> float:
    """Add two numbers together."""
    logger.debug(f"Adding {a} + {b}")
    return a + b


@mcp.tool
@handle_tool_errors
def multiply(
    a: Annotated[float, Field(description="First number")],
    b: Annotated[float, Field(description="Second number")]
) -> float:
    """Multiply two numbers together."""
    logger.debug(f"Multiplying {a} * {b}")
    return a * b


@mcp.tool
@handle_tool_errors
def advanced_math(
    operation: Literal["sqrt", "sin", "cos", "tan", "log", "exp"],
    value: Annotated[float, Field(description="Input value")]
) -> CalculationResult:
    """Perform advanced mathematical operations."""

    # Validate input based on operation
    if operation == "sqrt" and value < 0:
        raise ValidationError("Square root requires non-negative input")
    elif operation == "log" and value <= 0:
        raise ValidationError("Logarithm requires positive input")

    operations = {
        "sqrt": math.sqrt,
        "sin": math.sin,
        "cos": math.cos,
        "tan": math.tan,
        "log": math.log,
        "exp": math.exp
    }

    if operation not in operations:
        raise ValidationError(f"Unsupported operation: {operation}")

    logger.info(f"Performing {operation}({value})")
    result = operations[operation](value)

    return CalculationResult(
        operation=operation,
        inputs=[value],
        result=result,
        timestamp=datetime.now()
    )


@mcp.tool
@handle_tool_errors
def reverse_text(text: str) -> str:
    """Reverse the input text."""
    validate_string_length(text, max_length=10000, field_name="text")
    logger.debug(f"Reversing text of length {len(text)}")
    return text[::-1]


@mcp.tool
@handle_tool_errors
def word_count(
    text: str,
    include_spaces: bool = False
) -> Dict[str, int]:
    """Count words and characters in text."""
    validate_string_length(text, max_length=50000, field_name="text")

    words = len(text.split())
    chars = len(text)
    chars_no_spaces = len(text.replace(" ", ""))

    result = {
        "words": words,
        "characters": chars,
        "characters_no_spaces": chars_no_spaces
    }

    if include_spaces:
        result["spaces"] = chars - chars_no_spaces

    logger.info(f"Analyzed text: {words} words, {chars} characters")
    return result


@mcp.tool
@handle_tool_errors
def generate_data(
    count: Annotated[int, Field(description="Number of items to generate", ge=1, le=10)],
    data_type: Literal["numbers", "names", "emails"] = "numbers"
) -> List[Any]:
    """Generate sample data of specified type."""

    validate_numeric_range(count, min_value=1, max_value=10, field_name="count")

    logger.info(f"Generating {count} items of type '{data_type}'")

    if data_type == "numbers":
        return [random.randint(1, 1000) for _ in range(count)]
    elif data_type == "names":
        names = ["Alice", "Bob", "Charlie", "Diana", "Eve", "Frank", "Grace", "Henry"]
        return [random.choice(names) for _ in range(count)]
    elif data_type == "emails":
        domains = ["example.com", "test.org", "demo.net"]
        names = ["user", "admin", "test", "demo"]
        return [f"{random.choice(names)}{random.randint(1, 999)}@{random.choice(domains)}"
                for _ in range(count)]
    else:
        raise ValidationError(f"Unsupported data type: {data_type}")


@mcp.tool
def create_user(
    name: str,
    email: str,
    tags: Optional[List[str]] = None
) -> UserProfile:
    """Create a new user profile."""
    return UserProfile(
        id=random.randint(1000, 9999),
        name=name,
        email=email,
        created_at=datetime.now(),
        is_active=True,
        tags=tags or []
    )


@mcp.tool
@handle_async_tool_errors
async def read_file(
    file_path: str,
    ctx: Context
) -> str:
    """Read content from a file."""
    validate_file_path(file_path, must_exist=True)
    await ctx.info(f"Reading file: {file_path}")

    path = Path(file_path)

    # Additional safety checks
    if path.stat().st_size > 10 * 1024 * 1024:  # 10MB limit
        raise ValidationError("File too large (max 10MB)")

    async with aiofiles.open(path, 'r', encoding='utf-8') as f:
        content = await f.read()

    await ctx.info(f"Successfully read {len(content)} characters from {file_path}")
    logger.info(f"File read: {file_path} ({len(content)} chars)")
    return content


@mcp.tool
@handle_async_tool_errors
async def write_file(
    file_path: str,
    content: str,
    ctx: Context
) -> Dict[str, Any]:
    """Write content to a file."""
    validate_file_path(file_path)
    validate_string_length(content, max_length=10 * 1024 * 1024, field_name="content")

    await ctx.info(f"Writing to file: {file_path}")

    path = Path(file_path)
    path.parent.mkdir(parents=True, exist_ok=True)

    async with aiofiles.open(path, 'w', encoding='utf-8') as f:
        await f.write(content)

    file_size = path.stat().st_size
    await ctx.info(f"Successfully wrote {len(content)} characters to {file_path}")
    logger.info(f"File written: {file_path} ({len(content)} chars)")

    return {
        "file_path": str(path),
        "bytes_written": len(content.encode('utf-8')),
        "file_size": file_size,
        "timestamp": datetime.now().isoformat()
    }


@mcp.tool
@handle_async_tool_errors
async def fetch_url(
    url: str,
    ctx: Context,
    timeout: Annotated[int, Field(description="Request timeout in seconds", ge=1, le=30)] = 10
) -> Dict[str, Any]:
    """Fetch content from a URL."""
    # Basic URL validation
    if not url.startswith(("http://", "https://")):
        raise ValidationError("URL must start with http:// or https://")

    validate_numeric_range(timeout, min_value=1, max_value=30, field_name="timeout")

    await ctx.info(f"Fetching URL: {url}")
    logger.info(f"HTTP request: {url} (timeout: {timeout}s)")

    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
        async with session.get(url) as response:
            content = await response.text()

            # Limit content size
            if len(content) > 1024 * 1024:  # 1MB limit
                content = content[:1024 * 1024]
                truncated = True
            else:
                truncated = False

            result = {
                "url": url,
                "status_code": response.status,
                "content_length": len(content),
                "content_type": response.headers.get("content-type", "unknown"),
                "content": content[:1000] + "..." if len(content) > 1000 else content,
                "truncated": truncated,
                "timestamp": datetime.now().isoformat()
            }

            await ctx.info(f"Successfully fetched {len(content)} characters from {url}")
            logger.info(f"HTTP response: {response.status} from {url}")
            return result


@mcp.tool
def get_system_info() -> Dict[str, Any]:
    """Get system information."""
    return {
        "platform": os.name,
        "current_directory": str(Path.cwd()),
        "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
        "environment_variables": dict(os.environ),
        "timestamp": datetime.now().isoformat()
    }


# =============================================================================
# RESOURCES - Static and dynamic data sources
# =============================================================================

@mcp.resource("config://server")
def get_server_config() -> Dict[str, Any]:
    """Provides the server configuration."""
    return {
        "name": "FastMCP Demo Server",
        "version": "1.0.0",
        "features": [
            "mathematical_operations",
            "text_processing", 
            "file_operations",
            "network_requests",
            "data_generation"
        ],
        "limits": {
            "max_file_size": "10MB",
            "max_request_timeout": 30,
            "max_generated_items": 100
        },
        "startup_time": datetime.now().isoformat()
    }


@mcp.resource("status://health")
def get_health_status() -> Dict[str, Any]:
    """Provides server health status."""
    return {
        "status": "healthy",
        "uptime_seconds": time.time(),
        "memory_usage": "unknown",
        "active_connections": 1,
        "last_check": datetime.now().isoformat()
    }


@mcp.resource("data://sample/{data_type}")
def get_sample_data(data_type: str) -> List[Any]:
    """Provides sample data of different types."""
    samples = {
        "numbers": [1, 2, 3, 5, 8, 13, 21, 34, 55, 89],
        "colors": ["red", "green", "blue", "yellow", "purple", "orange"],
        "countries": ["USA", "Canada", "UK", "France", "Germany", "Japan"],
        "fruits": ["apple", "banana", "orange", "grape", "strawberry", "kiwi"]
    }
    
    return samples.get(data_type, [])


@mcp.resource("users://{user_id}/profile")
def get_user_profile(user_id: int) -> UserProfile:
    """Retrieves a user profile by ID."""
    # Simulate user data
    names = ["Alice Johnson", "Bob Smith", "Charlie Brown", "Diana Prince"]
    name = names[user_id % len(names)]
    
    return UserProfile(
        id=user_id,
        name=name,
        email=f"{name.lower().replace(' ', '.')}@example.com",
        created_at=datetime.now() - timedelta(days=random.randint(1, 365)),
        is_active=random.choice([True, True, True, False]),  # 75% active
        tags=random.sample(["admin", "user", "premium", "beta"], k=random.randint(1, 3))
    )


# =============================================================================
# SERVER STARTUP
# =============================================================================

if __name__ == "__main__":
    # Log server startup
    log_server_startup(logger, "FastMCP Demo Server", "stdio")

    logger.info("Server provides tools for math, text processing, file operations, and more!")
    logger.info("Use STDIO transport by default, or specify --transport http for web access")
    logger.info("Logs are saved to logs/fastmcp_demo.log")

    try:
        # Run the server with default STDIO transport
        mcp.run()
    except KeyboardInterrupt:
        logger.info("Server shutdown requested by user")
    except Exception as e:
        logger.error(f"Server crashed: {str(e)}")
        raise
