#!/usr/bin/env python3
"""
Install FastMCP Demo Server into Augment Code for VS Code

This script helps you install the FastMCP demo server into Augment Code
by creating the appropriate MCP configuration.
"""

import json
import os
import platform
import subprocess
import sys
from pathlib import Path


def get_vscode_config_path():
    """Get the VS Code configuration path for the current platform."""
    system = platform.system()
    
    if system == "Windows":
        base_path = Path(os.environ.get("APPDATA", "")) / "Code"
    elif system == "Darwin":  # macOS
        base_path = Path.home() / "Library" / "Application Support" / "Code"
    else:  # Linux
        base_path = Path.home() / ".config" / "Code"
    
    return base_path / "User" / "globalStorage" / "augmentcode.augment"


def create_mcp_config():
    """Create MCP configuration for Augment Code."""
    print("📝 Creating MCP configuration for Augment Code...")
    
    # Get current server path
    server_path = Path.cwd() / "server.py"
    
    config = {
        "mcpServers": {
            "fastmcp-demo-server": {
                "command": "python",
                "args": [str(server_path.absolute())],
                "env": {
                    "FASTMCP_LOG_LEVEL": "INFO"
                }
            }
        }
    }
    
    # Try to create the Augment Code config
    try:
        config_dir = get_vscode_config_path()
        config_dir.mkdir(parents=True, exist_ok=True)
        
        config_file = config_dir / "mcp_servers.json"
        
        # Check if config already exists
        existing_config = {}
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    existing_config = json.load(f)
                print(f"📄 Found existing configuration at: {config_file}")
            except json.JSONDecodeError:
                print("⚠️  Existing config file is invalid, will overwrite")
        
        # Merge configurations
        if "mcpServers" in existing_config:
            existing_config["mcpServers"]["fastmcp-demo-server"] = config["mcpServers"]["fastmcp-demo-server"]
        else:
            existing_config = config
        
        # Write configuration
        with open(config_file, 'w') as f:
            json.dump(existing_config, f, indent=2)
        
        print(f"✅ Configuration saved to: {config_file}")
        return config_file, existing_config
        
    except Exception as e:
        print(f"❌ Error creating Augment Code config: {e}")
        return None, config


def create_vscode_settings():
    """Create VS Code settings.json configuration."""
    print("\n📝 Creating VS Code settings configuration...")
    
    server_path = Path.cwd() / "server.py"
    
    settings_config = {
        "augmentcode.mcp.servers": {
            "fastmcp-demo-server": {
                "command": "python",
                "args": [str(server_path.absolute())],
                "env": {
                    "FASTMCP_LOG_LEVEL": "INFO"
                }
            }
        }
    }
    
    try:
        # Get VS Code settings path
        if platform.system() == "Windows":
            settings_dir = Path(os.environ.get("APPDATA", "")) / "Code" / "User"
        elif platform.system() == "Darwin":  # macOS
            settings_dir = Path.home() / "Library" / "Application Support" / "Code" / "User"
        else:  # Linux
            settings_dir = Path.home() / ".config" / "Code" / "User"
        
        settings_file = settings_dir / "settings.json"
        
        # Read existing settings
        existing_settings = {}
        if settings_file.exists():
            try:
                with open(settings_file, 'r') as f:
                    existing_settings = json.load(f)
            except json.JSONDecodeError:
                print("⚠️  Existing settings.json is invalid")
        
        # Merge settings
        existing_settings.update(settings_config)
        
        # Write settings
        with open(settings_file, 'w') as f:
            json.dump(existing_settings, f, indent=2)
        
        print(f"✅ VS Code settings updated: {settings_file}")
        return settings_file
        
    except Exception as e:
        print(f"❌ Error updating VS Code settings: {e}")
        return None


def create_manual_config():
    """Create manual configuration files."""
    print("\n📝 Creating manual configuration files...")
    
    server_path = Path.cwd() / "server.py"
    
    # Create Augment Code config
    augment_config = {
        "mcpServers": {
            "fastmcp-demo-server": {
                "command": "python",
                "args": [str(server_path.absolute())],
                "env": {
                    "FASTMCP_LOG_LEVEL": "INFO"
                }
            }
        }
    }
    
    # Save manual config
    manual_config_file = Path("augment_code_config.json")
    with open(manual_config_file, 'w') as f:
        json.dump(augment_config, f, indent=2)
    
    print(f"✅ Manual configuration saved to: {manual_config_file}")
    
    return manual_config_file, augment_config


def check_prerequisites():
    """Check if prerequisites are met."""
    print("🔍 Checking prerequisites...")
    
    # Check if server.py exists
    if not Path("server.py").exists():
        print("❌ server.py not found in current directory")
        return False
    
    # Check if Python is available
    try:
        result = subprocess.run(["python", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Python found: {result.stdout.strip()}")
        else:
            print("❌ Python not found in PATH")
            return False
    except FileNotFoundError:
        print("❌ Python not found in PATH")
        return False
    
    # Check if dependencies are installed
    try:
        import fastmcp
        print(f"✅ FastMCP found: {fastmcp.__version__}")
    except ImportError:
        print("❌ FastMCP not installed. Run: pip install -r requirements.txt")
        return False
    
    return True


def main():
    """Main installation function."""
    print("🚀 FastMCP Demo Server - Augment Code Integration")
    print("=" * 60)
    
    if not check_prerequisites():
        print("\n❌ Prerequisites not met. Please fix the issues above.")
        sys.exit(1)
    
    print("\nChoose installation method:")
    print("1. Automatic installation (recommended)")
    print("2. Manual configuration files only")
    print("3. Both automatic and manual")
    
    choice = input("\nEnter your choice (1, 2, or 3): ").strip()
    
    success = False
    
    if choice in ["1", "3"]:
        print("\n🔧 Attempting automatic installation...")
        
        # Try MCP config
        config_file, config = create_mcp_config()
        if config_file:
            success = True
        
        # Try VS Code settings
        settings_file = create_vscode_settings()
        if settings_file:
            success = True
    
    if choice in ["2", "3"] or not success:
        print("\n📁 Creating manual configuration files...")
        manual_file, manual_config = create_manual_config()
        
        print("\n📋 Manual Installation Steps:")
        print("1. Open VS Code")
        print("2. Install the Augment Code extension if not already installed")
        print("3. Open VS Code Settings (Ctrl+, or Cmd+,)")
        print("4. Search for 'augment mcp' or 'mcp servers'")
        print("5. Add the configuration from the generated file")
        print(f"\nConfiguration to add:")
        print(json.dumps(manual_config, indent=2))
    
    print("\n🎉 Integration setup complete!")
    print("\n📚 Next Steps:")
    print("1. Restart VS Code")
    print("2. Open the Augment Code extension")
    print("3. Your FastMCP demo server should be available")
    print("4. Try asking Augment to use tools like:")
    print("   - 'Add two numbers together'")
    print("   - 'Generate some sample data'")
    print("   - 'Reverse this text'")
    
    print(f"\n🔧 Troubleshooting:")
    print("- Check VS Code Output panel for Augment Code logs")
    print("- Verify server works: python server.py")
    print("- Check the integration guide: AUGMENT_CODE_INTEGRATION.md")


if __name__ == "__main__":
    main()
