#!/usr/bin/env python3
"""
Example usage of Claude MCP Client

This script demonstrates how to use the Claude MCP client programmatically
instead of in interactive mode.
"""

import async<PERSON>
import os
from claude_mcp_client import ClaudeMCPClient


async def example_math_operations():
    """Example: Using <PERSON> with MCP math tools."""
    print("🧮 Math Operations Example")
    print("=" * 40)
    
    # Get API key
    api_key = os.getenv("ANTHROPIC_API_KEY")
    if not api_key:
        print("❌ Please set ANTHROPIC_API_KEY environment variable")
        return
    
    # Create client
    client = ClaudeMCPClient(api_key=api_key)
    
    # Connect to MCP server
    if not await client.connect_mcp():
        return
    
    try:
        # Ask <PERSON> to perform calculations
        questions = [
            "What's 15 + 27?",
            "Calculate the square root of 64",
            "What's the sine of π/2?",
            "Multiply 6 by 7"
        ]
        
        for question in questions:
            print(f"\nQ: {question}")
            response = await client.chat_with_claude(question)
            print(f"A: {response}")
            
    finally:
        await client.disconnect_mcp()


async def example_text_processing():
    """Example: Using <PERSON> with MCP text tools."""
    print("\n📝 Text Processing Example")
    print("=" * 40)
    
    api_key = os.getenv("ANTHROPIC_API_KEY")
    if not api_key:
        print("❌ Please set ANTHROPIC_API_KEY environment variable")
        return
    
    client = ClaudeMCPClient(api_key=api_key)
    
    if not await client.connect_mcp():
        return
    
    try:
        # Ask Claude to process text
        tasks = [
            "Reverse the text 'Hello, World!'",
            "Count the words in 'The quick brown fox jumps over the lazy dog'",
            "Reverse 'FastMCP is awesome' and then count the words in the result"
        ]
        
        for task in tasks:
            print(f"\nTask: {task}")
            response = await client.chat_with_claude(task)
            print(f"Result: {response}")
            
    finally:
        await client.disconnect_mcp()


async def example_data_generation():
    """Example: Using Claude with MCP data generation tools."""
    print("\n🎲 Data Generation Example")
    print("=" * 40)
    
    api_key = os.getenv("ANTHROPIC_API_KEY")
    if not api_key:
        print("❌ Please set ANTHROPIC_API_KEY environment variable")
        return
    
    client = ClaudeMCPClient(api_key=api_key)
    
    if not await client.connect_mcp():
        return
    
    try:
        # Ask Claude to generate data
        requests = [
            "Generate 5 random names",
            "Create 3 random email addresses",
            "Generate 10 random numbers",
            "Create a user profile for John Doe <NAME_EMAIL>"
        ]
        
        for request in requests:
            print(f"\nRequest: {request}")
            response = await client.chat_with_claude(request)
            print(f"Generated: {response}")
            
    finally:
        await client.disconnect_mcp()


async def example_complex_workflow():
    """Example: Complex workflow using multiple tools."""
    print("\n🔄 Complex Workflow Example")
    print("=" * 40)
    
    api_key = os.getenv("ANTHROPIC_API_KEY")
    if not api_key:
        print("❌ Please set ANTHROPIC_API_KEY environment variable")
        return
    
    client = ClaudeMCPClient(api_key=api_key)
    
    if not await client.connect_mcp():
        return
    
    try:
        # Complex multi-step task
        complex_task = """
        I need you to:
        1. Generate 5 random numbers
        2. Add the first two numbers together
        3. Calculate the square root of the sum
        4. Create a summary text describing what you did
        5. Count the words in that summary
        
        Please walk me through each step.
        """
        
        print(f"Complex Task: {complex_task}")
        response = await client.chat_with_claude(complex_task)
        print(f"Claude's Response: {response}")
        
    finally:
        await client.disconnect_mcp()


async def main():
    """Run all examples."""
    print("🚀 Claude MCP Client Examples")
    print("=" * 50)
    print("These examples show how to use Claude with your MCP server tools.")
    print()
    
    # Run examples
    await example_math_operations()
    await example_text_processing()
    await example_data_generation()
    await example_complex_workflow()
    
    print("\n✨ All examples completed!")
    print("\nTry running the interactive client with:")
    print("python claude_mcp_client.py")


if __name__ == "__main__":
    asyncio.run(main())
