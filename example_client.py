#!/usr/bin/env python3
"""
Example client for FastMCP Demo Server

This script demonstrates how to interact with the FastMCP demo server
using the FastMCP client library. It shows examples of calling tools
and reading resources.
"""

import asyncio
import j<PERSON>
from typing import Any, Dict

from fastmcp import Client


async def demonstrate_tools(client: Client) -> None:
    """Demonstrate various tool capabilities."""
    print("\n🔧 TOOL DEMONSTRATIONS")
    print("=" * 50)
    
    # Mathematical operations
    print("\n📊 Mathematical Operations:")
    result = await client.call_tool("add", {"a": 15, "b": 27})
    print(f"  15 + 27 = {result}")
    
    result = await client.call_tool("multiply", {"a": 6, "b": 7})
    print(f"  6 × 7 = {result}")
    
    result = await client.call_tool("advanced_math", {"operation": "sqrt", "value": 64})
    print(f"  √64 = {result}")
    
    # Text processing
    print("\n📝 Text Processing:")
    result = await client.call_tool("reverse_text", {"text": "Hello, FastMCP!"})
    print(f"  Reversed: {result}")
    
    result = await client.call_tool("word_count", {
        "text": "The quick brown fox jumps over the lazy dog",
        "include_spaces": True
    })
    print(f"  Word count: {result}")
    
    # Data generation
    print("\n🎲 Data Generation:")
    result = await client.call_tool("generate_data", {
        "count": 5,
        "data_type": "names"
    })
    print(f"  Random names: {result}")
    
    result = await client.call_tool("generate_data", {
        "count": 3,
        "data_type": "emails"
    })
    print(f"  Random emails: {result}")
    
    # User creation
    print("\n👤 User Management:")
    result = await client.call_tool("create_user", {
        "name": "Alice Johnson",
        "email": "<EMAIL>",
        "tags": ["admin", "beta-tester"]
    })
    print(f"  Created user: {result}")


async def demonstrate_resources(client: Client) -> None:
    """Demonstrate resource access."""
    print("\n📄 RESOURCE DEMONSTRATIONS")
    print("=" * 50)
    
    # Server configuration
    print("\n⚙️ Server Configuration:")
    try:
        resources = await client.read_resource("config://server")
        if resources:
            config = resources[0].content
            print(f"  Server: {config.get('name', 'Unknown')}")
            print(f"  Version: {config.get('version', 'Unknown')}")
            print(f"  Features: {', '.join(config.get('features', []))}")
    except Exception as e:
        print(f"  Error reading config: {e}")
    
    # Health status
    print("\n💚 Health Status:")
    try:
        resources = await client.read_resource("status://health")
        if resources:
            health = resources[0].content
            print(f"  Status: {health.get('status', 'Unknown')}")
            print(f"  Uptime: {health.get('uptime_seconds', 0):.1f} seconds")
    except Exception as e:
        print(f"  Error reading health: {e}")
    
    # Sample data
    print("\n📊 Sample Data:")
    for data_type in ["numbers", "colors", "countries"]:
        try:
            resources = await client.read_resource(f"data://sample/{data_type}")
            if resources:
                data = resources[0].content
                print(f"  {data_type.title()}: {data}")
        except Exception as e:
            print(f"  Error reading {data_type}: {e}")
    
    # Dynamic user profiles
    print("\n👥 User Profiles:")
    for user_id in [1, 42, 123]:
        try:
            resources = await client.read_resource(f"users://{user_id}/profile")
            if resources:
                profile = resources[0].content
                print(f"  User {user_id}: {profile.get('name')} ({profile.get('email')})")
        except Exception as e:
            print(f"  Error reading user {user_id}: {e}")


async def demonstrate_error_handling(client: Client) -> None:
    """Demonstrate error handling capabilities."""
    print("\n❌ ERROR HANDLING DEMONSTRATIONS")
    print("=" * 50)
    
    # Invalid mathematical operation
    print("\n🔢 Invalid Math Operation:")
    try:
        result = await client.call_tool("advanced_math", {"operation": "sqrt", "value": -1})
        print(f"  Unexpected success: {result}")
    except Exception as e:
        print(f"  Expected error: {e}")
    
    # File operation with invalid path
    print("\n📁 Invalid File Path:")
    try:
        result = await client.call_tool("read_file", {"file_path": "../../../etc/passwd"})
        print(f"  Unexpected success: {result}")
    except Exception as e:
        print(f"  Expected error: {e}")
    
    # Invalid URL
    print("\n🌐 Invalid URL:")
    try:
        result = await client.call_tool("fetch_url", {"url": "not-a-url", "timeout": 5})
        print(f"  Unexpected success: {result}")
    except Exception as e:
        print(f"  Expected error: {e}")


async def list_available_capabilities(client: Client) -> None:
    """List all available tools and resources."""
    print("\n📋 AVAILABLE CAPABILITIES")
    print("=" * 50)
    
    # List tools
    print("\n🔧 Available Tools:")
    try:
        tools = await client.list_tools()
        for tool in tools:
            print(f"  • {tool.name}: {tool.description}")
    except Exception as e:
        print(f"  Error listing tools: {e}")
    
    # List resources
    print("\n📄 Available Resources:")
    try:
        resources = await client.list_resources()
        for resource in resources:
            print(f"  • {resource.uri}: {resource.description}")
    except Exception as e:
        print(f"  Error listing resources: {e}")


async def main():
    """Main demonstration function."""
    print("🚀 FastMCP Demo Server Client")
    print("=" * 50)
    print("This client demonstrates the capabilities of the FastMCP demo server.")
    print("Make sure the server is running before executing this client.")
    
    # Connect to the server
    # For STDIO transport (default):
    client = Client("server.py")
    
    # For HTTP transport, use:
    # client = Client("http://localhost:8000/mcp/")
    
    try:
        async with client:
            # Test basic connectivity
            print("\n🔗 Testing connectivity...")
            await client.ping()
            print("✅ Connected successfully!")
            
            # Run demonstrations
            await list_available_capabilities(client)
            await demonstrate_tools(client)
            await demonstrate_resources(client)
            await demonstrate_error_handling(client)
            
            print("\n✨ All demonstrations completed successfully!")
            
    except Exception as e:
        print(f"\n💥 Error connecting to server: {e}")
        print("Make sure the server is running with: python server.py")


if __name__ == "__main__":
    asyncio.run(main())
