# FastMCP 2.0 Demo Server

A comprehensive demonstration of FastMCP 2.0 capabilities, showcasing how to build a production-ready Model Context Protocol (MCP) server using Python.

## 🚀 Features

This demo server demonstrates the following FastMCP 2.0 capabilities:

### 🔧 Tools (Functions LLMs can execute)

- **Mathematical Operations**: Basic arithmetic and advanced math functions
- **Text Processing**: Text reversal, word counting, and analysis
- **Data Generation**: Create sample datasets of various types
- **File Operations**: Read and write files with safety checks
- **Network Operations**: Fetch content from URLs with validation
- **System Information**: Get system and environment details

### 📄 Resources (Data sources for LLMs)

- **Server Configuration**: Runtime configuration and feature information
- **Health Status**: Server health and uptime monitoring
- **Sample Data**: Pre-defined datasets for testing and examples
- **Dynamic User Profiles**: Template-based user data generation

### 🛡️ Production Features

- **Enhanced Error Handling**: Custom exceptions and validation
- **Structured Logging**: Colored console output and file logging
- **Input Validation**: Comprehensive parameter validation
- **Safety Limits**: File size, request timeout, and data limits
- **Context Integration**: Progress reporting and LLM sampling

## 📦 Installation

### Prerequisites

- Python 3.8 or higher
- pip or uv package manager

### Setup

1. **Clone or download this project**

   ```bash
   git clone <repository-url>
   cd fastmcp-demo-server
   ```

2. **Install dependencies**

   ```bash
   pip install -r requirements.txt
   ```

   Or using uv (recommended):

   ```bash
   uv pip install -r requirements.txt
   ```

3. **Verify installation**
   ```bash
   python server.py --help
   ```

## 🔗 AI Assistant Integrations

### Augment Code (VS Code) - Recommended

1. **Run the installation script:**

   ```bash
   python install_augment_code.py
   ```

2. **Restart VS Code** and start using your tools with Augment Code!

**📚 Detailed Guide:** See `AUGMENT_CODE_INTEGRATION.md` for complete setup instructions.

### Claude Code Integration

1. **Run the installation helper:**

   ```bash
   python install_claude_code.py
   ```

2. **Or manually add to Claude Code settings:**

   ```json
   {
   	"mcpServers": {
   		"fastmcp-demo-server": {
   			"command": "python",
   			"args": ["path/to/your/server.py"],
   			"env": {
   				"FASTMCP_LOG_LEVEL": "INFO"
   			}
   		}
   	}
   }
   ```

3. **Restart Claude Code** and start using your tools!

**📚 Detailed Guide:** See `CLAUDE_CODE_INTEGRATION.md` for complete setup instructions.

## 🎯 Usage

### Running the Server

#### STDIO Transport (Default)

Perfect for local tools and Claude Desktop integration:

```bash
python server.py
```

#### HTTP Transport

For web-based deployments and remote access:

```bash
python server.py --transport http --port 8000
```

#### Using FastMCP CLI

```bash
# Run with default settings
fastmcp run server.py

# Run with HTTP transport
fastmcp run server.py --transport http --port 8000

# Development mode with inspector
fastmcp dev server.py
```

### Testing the Server

#### Using the FastMCP Client

```python
import asyncio
from fastmcp import Client

async def test_server():
    async with Client("server.py") as client:
        # Test a simple tool
        result = await client.call_tool("add", {"a": 5, "b": 3})
        print(f"5 + 3 = {result}")

        # Test a resource
        config = await client.read_resource("config://server")
        print(f"Server config: {config}")

asyncio.run(test_server())
```

#### Manual Testing

```bash
# Test with curl (HTTP transport)
curl -X POST http://localhost:8000/mcp/ \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc": "2.0", "id": 1, "method": "tools/list"}'
```

## 🔧 Configuration

### Environment Variables

Set these environment variables to customize behavior:

```bash
# Logging configuration
export FASTMCP_LOG_LEVEL=DEBUG
export FASTMCP_MASK_ERROR_DETAILS=False

# Server configuration
export FASTMCP_RESOURCE_PREFIX_FORMAT=protocol
```

### Logging

Logs are written to both console and file:

- **Console**: Colored output with timestamps
- **File**: `logs/fastmcp_demo.log` with detailed information

Log levels: `DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`

## 📚 API Reference

### Available Tools

| Tool              | Description                | Parameters                              |
| ----------------- | -------------------------- | --------------------------------------- |
| `add`             | Add two numbers            | `a: float`, `b: float`                  |
| `multiply`        | Multiply two numbers       | `a: float`, `b: float`                  |
| `advanced_math`   | Advanced math operations   | `operation: str`, `value: float`        |
| `reverse_text`    | Reverse text string        | `text: str`                             |
| `word_count`      | Count words and characters | `text: str`, `include_spaces: bool`     |
| `generate_data`   | Generate sample data       | `count: int`, `data_type: str`          |
| `create_user`     | Create user profile        | `name: str`, `email: str`, `tags: list` |
| `read_file`       | Read file content          | `file_path: str`                        |
| `write_file`      | Write file content         | `file_path: str`, `content: str`        |
| `fetch_url`       | Fetch URL content          | `url: str`, `timeout: int`              |
| `get_system_info` | Get system information     | None                                    |

### Available Resources

| Resource URI           | Description                       |
| ---------------------- | --------------------------------- |
| `config://server`      | Server configuration and features |
| `status://health`      | Server health and uptime          |
| `data://sample/{type}` | Sample data by type               |
| `users://{id}/profile` | User profile by ID                |

## 🛠️ Development

### Project Structure

```
fastmcp-demo-server/
├── server.py              # Main MCP server
├── logging_config.py      # Logging configuration
├── error_handling.py      # Error handling utilities
├── requirements.txt       # Python dependencies
├── package.json          # Node.js metadata
├── README.md             # This file
├── .gitignore            # Git ignore rules
└── logs/                 # Log files directory
```

### Adding New Tools

```python
@mcp.tool
@handle_tool_errors  # Add error handling
def my_new_tool(param: str) -> str:
    """Tool description for the LLM."""
    logger.info(f"Executing my_new_tool with {param}")
    return f"Result: {param}"
```

### Adding New Resources

```python
@mcp.resource("my://resource/uri")
def my_resource() -> dict:
    """Resource description."""
    return {"data": "value"}
```

## 🔒 Security Considerations

This demo server includes several security measures:

- **File Path Validation**: Prevents directory traversal attacks
- **Size Limits**: Restricts file and content sizes
- **Input Validation**: Validates all parameters
- **Timeout Controls**: Prevents hanging requests
- **Error Masking**: Option to hide internal error details

For production use, consider additional measures:

- Authentication and authorization
- Rate limiting
- Network security (HTTPS, firewalls)
- Input sanitization for specific use cases

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

- **FastMCP Documentation**: https://gofastmcp.com/
- **MCP Specification**: https://modelcontextprotocol.io/
- **Issues**: Create an issue in the repository

## 🎉 Acknowledgments

Built with [FastMCP 2.0](https://gofastmcp.com/) - The fast, Pythonic way to build MCP servers and clients.
