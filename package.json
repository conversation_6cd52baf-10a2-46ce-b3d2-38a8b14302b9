{"name": "fastmcp-demo-server", "version": "1.0.0", "description": "A demonstration MCP server built with FastMCP 2.0", "main": "server.py", "scripts": {"start": "python server.py", "dev": "fastmcp dev server.py", "test": "python test_server.py"}, "keywords": ["mcp", "fastmcp", "model-context-protocol", "ai", "llm"], "author": "FastMCP Demo", "license": "MIT", "engines": {"node": ">=18.0.0"}, "devDependencies": {}, "dependencies": {}}