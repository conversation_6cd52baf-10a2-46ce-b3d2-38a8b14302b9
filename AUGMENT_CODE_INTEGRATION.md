# Augment Code Integration Guide

This guide shows you how to integrate your FastMCP Demo Server with Augment Code inside VS Code, allowing you to use your custom tools directly within your development environment.

## 🚀 Quick Start

### Method 1: Using MCP Configuration (Recommended)

Augment Code supports MCP servers through standard MCP configuration. Here's how to set it up:

1. **Create MCP Configuration File**

   Create or edit your MCP configuration file. The location depends on your system:

   **Windows:**

   ```
   %APPDATA%\Code\User\globalStorage\augmentcode.augment\mcp_servers.json
   ```

   **macOS:**

   ```
   ~/Library/Application Support/Code/User/globalStorage/augmentcode.augment/mcp_servers.json
   ```

   **Linux:**

   ```
   ~/.config/Code/User/globalStorage/augmentcode.augment/mcp_servers.json
   ```

2. **Add Server Configuration**

   Add this configuration to the file:

   ```json
   {
   	"mcpServers": {
   		"fastmcp-demo-server": {
   			"command": "python",
   			"args": ["E:\\Code\\PythonApps\\testMCP\\server.py"],
   			"env": {
   				"FASTMCP_LOG_LEVEL": "INFO"
   			}
   		}
   	}
   }
   ```

3. **Restart VS Code** and the Augment Code extension will pick up your server.

### Method 2: Using VS Code Settings

1. **Open VS Code Settings:**

   - Press `Ctrl + ,` (Windows/Linux) or `Cmd + ,` (Mac)
   - Or go to `File` > `Preferences` > `Settings`

2. **Search for Augment Code MCP settings:**

   - Search for "augment mcp" or "mcp servers"
   - Look for Augment Code extension settings

3. **Add Server Configuration:**
   Add your server configuration in the Augment Code MCP settings section.

## 🛠️ Installation Helper Script

Use the automated setup script for easy installation:

```bash
python install_augment_code.py
```

This script will:

- ✅ Check prerequisites (Python, FastMCP, server.py)
- ✅ Create MCP configuration files
- ✅ Update VS Code settings if possible
- ✅ Provide manual setup instructions if needed

## 📋 Manual Setup Steps

If the automated script doesn't work, follow these manual steps:

### Step 1: Locate Configuration Directory

Find your VS Code configuration directory:

**Windows:**

```
%APPDATA%\Code\User\globalStorage\augmentcode.augment\
```

**macOS:**

```
~/Library/Application Support/Code/User/globalStorage/augmentcode.augment/
```

**Linux:**

```
~/.config/Code/User/globalStorage/augmentcode.augment/
```

### Step 2: Create MCP Configuration

Create a file called `mcp_servers.json` in the configuration directory:

```json
{
	"mcpServers": {
		"fastmcp-demo-server": {
			"command": "python",
			"args": ["E:\\Code\\PythonApps\\testMCP\\server.py"],
			"env": {
				"FASTMCP_LOG_LEVEL": "INFO"
			}
		}
	}
}
```

**Important:** Replace the path with your actual server.py location.

### Step 3: Alternative - VS Code Settings

You can also add the configuration to your VS Code `settings.json`:

1. Open VS Code Settings (`Ctrl+,` or `Cmd+,`)
2. Click "Open Settings (JSON)" in the top right
3. Add this configuration:

```json
{
	"augmentcode.mcp.servers": {
		"fastmcp-demo-server": {
			"command": "python",
			"args": ["path/to/your/server.py"],
			"env": {
				"FASTMCP_LOG_LEVEL": "INFO"
			}
		}
	}
}
```

## 🔍 Verification Steps

### 1. Check Augment Code Extension

1. **Install Augment Code** if not already installed:

   - Open VS Code Extensions (`Ctrl+Shift+X`)
   - Search for "Augment Code"
   - Install the extension

2. **Check Extension Status:**
   - Look for Augment Code in the status bar
   - Check for any error indicators

### 2. Test the Integration

Try these commands with Augment Code:

```
"Add the numbers 15 and 27"
"Generate 5 random names for testing"
"Reverse the text 'Hello Augment'"
"Count words in this code comment"
"What tools do you have available?"
```

### 3. Check Logs

1. **Open Output Panel:**

   - Press `Ctrl+Shift+U` (Windows/Linux) or `Cmd+Shift+U` (Mac)
   - Select "Augment Code" from the dropdown

2. **Look for MCP Server Logs:**
   - Server startup messages
   - Tool execution logs
   - Any error messages

## 🛠️ Available Tools in Augment Code

Once integrated, Augment Code will have access to all your FastMCP tools:

### 🧮 Mathematical Operations

- **Add/Multiply:** "Add 25 and 17", "Multiply 8 by 7"
- **Advanced Math:** "Calculate square root of 144", "Find sine of 45 degrees"

### 📝 Text Processing

- **Text Manipulation:** "Reverse this variable name", "Count characters in this string"
- **Code Analysis:** "Analyze this function name", "Process this text data"

### 🎲 Data Generation

- **Test Data:** "Generate sample user data", "Create test email addresses"
- **Mock Data:** "Generate random numbers for testing"

### 📁 File Operations

- **File Reading:** "Read the contents of config.json"
- **File Writing:** "Save this data structure to a file"

### 🌐 Network & System

- **Web Requests:** "Fetch data from this API endpoint"
- **System Info:** "Get current system information"

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. Server Not Starting

**Problem:** Augment Code can't start your MCP server

**Solutions:**

- Verify Python is in PATH: `python --version`
- Check server works standalone: `python server.py`
- Use absolute paths in configuration
- Install dependencies: `pip install -r requirements.txt`

#### 2. Tools Not Available

**Problem:** Augment Code doesn't see your tools

**Solutions:**

- Check Augment Code output panel for errors
- Verify MCP configuration syntax
- Restart VS Code after configuration changes
- Test server inspection: `fastmcp inspect server.py`

#### 3. Permission Issues

**Problem:** Permission denied errors

**Solutions:**

- Run VS Code with appropriate permissions
- Check file permissions on server.py
- Ensure Python environment is accessible

#### 4. Path Issues

**Problem:** "File not found" errors

**Solutions:**

- Use absolute paths: `C:\\full\\path\\to\\server.py`
- Escape backslashes in Windows paths
- Verify path exists: `ls /path/to/server.py`

### Debug Configuration

Enable debug logging for troubleshooting:

```json
{
	"mcpServers": {
		"fastmcp-demo-server": {
			"command": "python",
			"args": ["path/to/server.py"],
			"env": {
				"FASTMCP_LOG_LEVEL": "DEBUG"
			}
		}
	}
}
```

## 📋 Configuration Templates

### For Windows Development

```json
{
	"mcpServers": {
		"fastmcp-demo-server": {
			"command": "python",
			"args": ["C:\\Code\\PythonApps\\testMCP\\server.py"],
			"env": {
				"FASTMCP_LOG_LEVEL": "INFO",
				"PYTHONPATH": "C:\\Code\\PythonApps\\testMCP"
			}
		}
	}
}
```

### For macOS/Linux Development

```json
{
	"mcpServers": {
		"fastmcp-demo-server": {
			"command": "python3",
			"args": ["/home/<USER>/projects/testMCP/server.py"],
			"env": {
				"FASTMCP_LOG_LEVEL": "INFO",
				"PYTHONPATH": "/home/<USER>/projects/testMCP"
			}
		}
	}
}
```

### With Virtual Environment

```json
{
	"mcpServers": {
		"fastmcp-demo-server": {
			"command": "/path/to/venv/bin/python",
			"args": ["/path/to/server.py"],
			"env": {
				"FASTMCP_LOG_LEVEL": "INFO"
			}
		}
	}
}
```

## 🎯 Usage Examples in Development

### Code Generation

```
"Generate a Python function that adds two numbers"
"Create a test case for this function"
"Generate sample JSON data for this API"
```

### Code Analysis

```
"Count the complexity of this function"
"Analyze this code structure"
"Reverse engineer this algorithm"
```

### Development Workflow

```
"Create user test data for this feature"
"Generate configuration for this service"
"Calculate the performance metrics"
```

## 🔄 Updating the Server

When you make changes to your FastMCP server:

1. **Save changes** to server.py
2. **Restart the MCP server** (Augment Code should handle this automatically)
3. **Test changes** with a simple tool call
4. **Check logs** in VS Code Output panel if issues occur

## 📚 Additional Resources

- **Augment Code Documentation:** Check VS Code extension documentation
- **FastMCP Documentation:** https://gofastmcp.com/
- **MCP Specification:** https://modelcontextprotocol.io/
- **VS Code Extension API:** For advanced integrations

## 🎉 Success Indicators

You'll know the integration is working when:

- ✅ Augment Code responds to tool requests
- ✅ No error messages in VS Code Output panel
- ✅ Tools appear when you ask "What tools are available?"
- ✅ Server logs show incoming MCP requests
- ✅ Mathematical operations work: "Add 10 and 20"

## 🚀 Next Steps

Once integrated successfully:

1. **Explore tool capabilities** with natural language requests
2. **Integrate into your workflow** for code generation and analysis
3. **Extend the server** with project-specific tools
4. **Share configurations** with your team for consistent tooling

Your FastMCP demo server is now ready to enhance your VS Code development experience with Augment Code! 🎉
