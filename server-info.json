{"name": "FastMCP Demo Server", "instructions": "\n    This is a demonstration server showcasing FastMCP 2.0 capabilities.\n\n    Available tools:\n    - Mathematical calculations (add, multiply, advanced_math)\n    - Text processing (reverse_text, word_count)\n    - Data generation (generate_data, create_user)\n    - File operations (read_file, write_file)\n    - Network operations (fetch_url)\n    - System information (get_system_info)\n\n    Available resources:\n    - Server configuration and status\n    - Dynamic user profiles\n    - Sample data sets\n    ", "fastmcp_version": "2.10.5", "mcp_version": "1.11.0", "server_version": "2.10.5", "tools": [{"key": "add", "name": "add", "description": "Add two numbers together.", "input_schema": {"properties": {"a": {"title": "A", "type": "number"}, "b": {"title": "B", "type": "number"}}, "required": ["a", "b"], "type": "object"}, "annotations": null, "tags": null, "enabled": true}, {"key": "multiply", "name": "multiply", "description": "Multiply two numbers together.", "input_schema": {"properties": {"a": {"description": "First number", "title": "A", "type": "number"}, "b": {"description": "Second number", "title": "B", "type": "number"}}, "required": ["a", "b"], "type": "object"}, "annotations": null, "tags": null, "enabled": true}, {"key": "advanced_math", "name": "advanced_math", "description": "Perform advanced mathematical operations.", "input_schema": {"properties": {"operation": {"enum": ["sqrt", "sin", "cos", "tan", "log", "exp"], "title": "Operation", "type": "string"}, "value": {"description": "Input value", "title": "Value", "type": "number"}}, "required": ["operation", "value"], "type": "object"}, "annotations": null, "tags": null, "enabled": true}, {"key": "reverse_text", "name": "reverse_text", "description": "Reverse the input text.", "input_schema": {"properties": {"text": {"title": "Text", "type": "string"}}, "required": ["text"], "type": "object"}, "annotations": null, "tags": null, "enabled": true}, {"key": "word_count", "name": "word_count", "description": "Count words and characters in text.", "input_schema": {"properties": {"text": {"title": "Text", "type": "string"}, "include_spaces": {"default": false, "title": "Include Spaces", "type": "boolean"}}, "required": ["text"], "type": "object"}, "annotations": null, "tags": null, "enabled": true}, {"key": "generate_data", "name": "generate_data", "description": "Generate sample data of specified type.", "input_schema": {"properties": {"count": {"description": "Number of items to generate", "maximum": 100, "minimum": 1, "title": "Count", "type": "integer"}, "data_type": {"default": "numbers", "enum": ["numbers", "names", "emails"], "title": "Data Type", "type": "string"}}, "required": ["count"], "type": "object"}, "annotations": null, "tags": null, "enabled": true}, {"key": "create_user", "name": "create_user", "description": "Create a new user profile.", "input_schema": {"properties": {"name": {"title": "Name", "type": "string"}, "email": {"title": "Email", "type": "string"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Tags"}}, "required": ["name", "email"], "type": "object"}, "annotations": null, "tags": null, "enabled": true}, {"key": "read_file", "name": "read_file", "description": "Read content from a file.", "input_schema": {"properties": {"file_path": {"title": "File Path", "type": "string"}}, "required": ["file_path"], "type": "object"}, "annotations": null, "tags": null, "enabled": true}, {"key": "write_file", "name": "write_file", "description": "Write content to a file.", "input_schema": {"properties": {"file_path": {"title": "File Path", "type": "string"}, "content": {"title": "Content", "type": "string"}}, "required": ["file_path", "content"], "type": "object"}, "annotations": null, "tags": null, "enabled": true}, {"key": "fetch_url", "name": "fetch_url", "description": "Fetch content from a URL.", "input_schema": {"properties": {"url": {"title": "Url", "type": "string"}, "timeout": {"default": 10, "description": "Request timeout in seconds", "maximum": 30, "minimum": 1, "title": "Timeout", "type": "integer"}}, "required": ["url"], "type": "object"}, "annotations": null, "tags": null, "enabled": true}, {"key": "get_system_info", "name": "get_system_info", "description": "Get system information.", "input_schema": {"properties": {}, "type": "object"}, "annotations": null, "tags": null, "enabled": true}], "prompts": [], "resources": [{"key": "config://server", "uri": "config://server", "name": "get_server_config", "description": "Provides the server configuration.", "mime_type": "text/plain", "tags": null, "enabled": true}, {"key": "status://health", "uri": "status://health", "name": "get_health_status", "description": "Provides server health status.", "mime_type": "text/plain", "tags": null, "enabled": true}], "templates": [{"key": "data://sample/{data_type}", "uri_template": "data://sample/{data_type}", "name": "get_sample_data", "description": "Provides sample data of different types.", "mime_type": "text/plain", "tags": null, "enabled": true}, {"key": "users://{user_id}/profile", "uri_template": "users://{user_id}/profile", "name": "get_user_profile", "description": "Retrieves a user profile by ID.", "mime_type": "text/plain", "tags": null, "enabled": true}], "capabilities": {"tools": {"listChanged": true}, "resources": {"subscribe": false, "listChanged": false}, "prompts": {"listChanged": false}, "logging": {}}}