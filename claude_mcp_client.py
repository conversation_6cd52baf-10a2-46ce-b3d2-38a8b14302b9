#!/usr/bin/env python3
"""
Claude MCP CLI Client

A command-line interface that integrates Claude API with your FastMCP server.
Users can chat with <PERSON>, and <PERSON> can use the MCP server tools to provide enhanced responses.
"""

import asyncio
import json
import os
import sys
from typing import Any, Dict, List
import argparse

import anthropic
from fastmcp import Client
from dotenv import load_dotenv


class ClaudeMCPClient:
    """CLI client that connects Claude API with FastMCP server."""
    
    def __init__(self, api_key: str, mcp_server_path: str = "server.py", model: str = "claude-3-5-sonnet-20241022"):
        """Initialize the client with Claude API and MCP server connection."""
        self.claude_client = anthropic.Anthropic(api_key=api_key)
        self.mcp_server_path = mcp_server_path
        self.model = model
        self.mcp_client = None
        self.conversation_history = []
        self.available_tools = []
        
    async def connect_mcp(self):
        """Connect to the MCP server and get available tools."""
        try:
            self.mcp_client = Client(self.mcp_server_path)
            await self.mcp_client.__aenter__()
            
            # Get available tools and convert them to Claude tool format
            tools = await self.mcp_client.list_tools()
            self.available_tools = self._convert_mcp_tools_to_claude_format(tools)
            
            print(f"✅ Connected to MCP server with {len(self.available_tools)} tools available")
            return True
            
        except Exception as e:
            print(f"❌ Failed to connect to MCP server: {e}")
            return False
    
    async def disconnect_mcp(self):
        """Disconnect from the MCP server."""
        if self.mcp_client:
            try:
                await self.mcp_client.__aexit__(None, None, None)
                print("✅ Disconnected from MCP server")
            except Exception as e:
                print(f"⚠️ Error disconnecting from MCP server: {e}")
    
    def _convert_mcp_tools_to_claude_format(self, mcp_tools) -> List[Dict[str, Any]]:
        """Convert MCP tools to Claude API tool format."""
        claude_tools = []
        
        for tool in mcp_tools:
            claude_tool = {
                "name": tool.name,
                "description": tool.description,
                "input_schema": tool.inputSchema
            }
            claude_tools.append(claude_tool)
        
        return claude_tools
    
    async def call_mcp_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """Call an MCP tool and return the result."""
        try:
            result = await self.mcp_client.call_tool(tool_name, arguments)

            # Extract serializable content from CallToolResult
            if hasattr(result, 'is_error') and result.is_error:
                # Handle error case
                if hasattr(result, 'content') and result.content:
                    # Safely extract text from content
                    first_content = result.content[0]
                    error_text = getattr(first_content, 'text', str(first_content))
                    return f"Tool error: {error_text}"
                return "Tool execution failed"

            # Return the structured content if available, otherwise the data
            if hasattr(result, 'structured_content') and result.structured_content:
                return result.structured_content
            elif hasattr(result, 'data'):
                return result.data
            elif hasattr(result, 'content') and result.content:
                # Safely extract text from content
                first_content = result.content[0]
                return getattr(first_content, 'text', str(first_content))
            else:
                return str(result)

        except Exception as e:
            return f"Error calling tool {tool_name}: {str(e)}"
    
    async def chat_with_claude(self, user_message: str) -> str:
        """Send a message to Claude and handle any tool calls."""
        # Add user message to conversation history
        self.conversation_history.append({
            "role": "user",
            "content": user_message
        })
        
        try:
            # Make the initial request to Claude
            response = self.claude_client.messages.create(
                model=self.model,
                max_tokens=4096,
                tools=self.available_tools,
                messages=self.conversation_history
            )
            
            # Handle the response
            assistant_message = {"role": "assistant", "content": []}
            
            for content_block in response.content:
                if content_block.type == "text":
                    assistant_message["content"].append({
                        "type": "text",
                        "text": content_block.text
                    })
                elif content_block.type == "tool_use":
                    # Claude wants to use a tool
                    tool_name = content_block.name
                    tool_input = content_block.input
                    tool_use_id = content_block.id
                    
                    print(f"🔧 Claude is using tool: {tool_name}")
                    
                    # Call the MCP tool
                    tool_result = await self.call_mcp_tool(tool_name, tool_input)
                    
                    # Add tool use to assistant message
                    assistant_message["content"].append({
                        "type": "tool_use",
                        "id": tool_use_id,
                        "name": tool_name,
                        "input": tool_input
                    })
                    
                    # Add tool result message
                    tool_result_message = {
                        "role": "user",
                        "content": [{
                            "type": "tool_result",
                            "tool_use_id": tool_use_id,
                            "content": json.dumps(tool_result) if not isinstance(tool_result, str) else tool_result
                        }]
                    }
                    
                    # Add messages to history
                    self.conversation_history.append(assistant_message)
                    self.conversation_history.append(tool_result_message)
                    
                    # Get Claude's response after tool use
                    follow_up_response = self.claude_client.messages.create(
                        model=self.model,
                        max_tokens=4096,
                        tools=self.available_tools,
                        messages=self.conversation_history
                    )
                    
                    # Extract the final response text
                    final_response = ""
                    for block in follow_up_response.content:
                        if block.type == "text":
                            final_response += block.text
                    
                    # Add final response to history
                    self.conversation_history.append({
                        "role": "assistant",
                        "content": final_response
                    })
                    
                    return final_response
            
            # If no tools were used, just return the text response
            response_text = ""
            for block in response.content:
                if block.type == "text":
                    response_text += block.text
            
            self.conversation_history.append({
                "role": "assistant",
                "content": response_text
            })
            
            return response_text
            
        except Exception as e:
            error_msg = f"Error communicating with Claude: {str(e)}"
            print(f"❌ {error_msg}")
            return error_msg
    
    def print_available_tools(self):
        """Print information about available MCP tools."""
        if not self.available_tools:
            print("No tools available")
            return
            
        print("\n🔧 Available MCP Tools:")
        print("=" * 50)
        for tool in self.available_tools:
            print(f"• {tool['name']}: {tool['description']}")
        print()
    
    async def run_interactive_session(self):
        """Run an interactive chat session."""
        print("🤖 Claude MCP CLI Client")
        print("=" * 50)
        print("Chat with Claude! Claude can use your MCP server tools to help you.")
        print("Type 'quit', 'exit', or 'bye' to end the session.")
        print("Type 'tools' to see available MCP tools.")
        print("Type 'clear' to clear conversation history.")
        print()
        
        # Connect to MCP server
        if not await self.connect_mcp():
            print("Cannot continue without MCP server connection.")
            return
        
        try:
            while True:
                # Get user input
                user_input = input("You: ").strip()
                
                if not user_input:
                    continue
                
                # Handle special commands
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    print("👋 Goodbye!")
                    break
                elif user_input.lower() == 'tools':
                    self.print_available_tools()
                    continue
                elif user_input.lower() == 'clear':
                    self.conversation_history = []
                    print("🧹 Conversation history cleared.")
                    continue
                
                # Send message to Claude
                print("Claude: ", end="", flush=True)
                response = await self.chat_with_claude(user_input)
                print(response)
                print()
                
        except KeyboardInterrupt:
            print("\n👋 Session interrupted. Goodbye!")
        finally:
            await self.disconnect_mcp()


async def main():
    """Main function to run the CLI client."""
    # Load environment variables from .env file
    load_dotenv()

    parser = argparse.ArgumentParser(description="Claude MCP CLI Client")
    parser.add_argument("--api-key", help="Claude API key (or set ANTHROPIC_API_KEY env var)")
    parser.add_argument("--server", default="server.py", help="Path to MCP server script")
    parser.add_argument("--model", default="claude-3-5-sonnet-20241022", help="Claude model to use")

    args = parser.parse_args()

    # Get API key
    api_key = args.api_key or os.getenv("ANTHROPIC_API_KEY")
    if not api_key:
        print("❌ Error: Claude API key required!")
        print("Set ANTHROPIC_API_KEY environment variable or use --api-key argument")
        sys.exit(1)
    
    # Create and run client
    client = ClaudeMCPClient(
        api_key=api_key,
        mcp_server_path=args.server,
        model=args.model
    )
    
    await client.run_interactive_session()


if __name__ == "__main__":
    asyncio.run(main())
