#!/usr/bin/env python3
"""
Comprehensive test suite for FastMCP Demo Server

This script runs a comprehensive test of the FastMCP demo server,
testing various tools, resources, and error handling scenarios.
"""

import asyncio
import json
import time
from fastmcp import Client


async def test_mathematical_tools(client: Client):
    """Test mathematical operation tools."""
    print("\n🧮 Testing Mathematical Tools:")
    
    # Test basic arithmetic
    result = await client.call_tool("add", {"a": 15, "b": 25})
    print(f"  ✅ add(15, 25) = {result.data}")
    
    result = await client.call_tool("multiply", {"a": 7, "b": 8})
    print(f"  ✅ multiply(7, 8) = {result.data}")
    
    # Test advanced math
    result = await client.call_tool("advanced_math", {"operation": "sqrt", "value": 144})
    print(f"  ✅ sqrt(144) = {result.data}")
    
    # Test error handling
    try:
        await client.call_tool("advanced_math", {"operation": "sqrt", "value": -1})
        print("  ❌ Should have failed for negative sqrt")
    except Exception as e:
        print(f"  ✅ Correctly handled error: {type(e).__name__}")


async def test_text_processing_tools(client: Client):
    """Test text processing tools."""
    print("\n📝 Testing Text Processing Tools:")
    
    # Test text reversal
    result = await client.call_tool("reverse_text", {"text": "FastMCP"})
    print(f"  ✅ reverse_text('FastMCP') = '{result.data}'")
    
    # Test word count
    result = await client.call_tool("word_count", {
        "text": "The quick brown fox jumps over the lazy dog",
        "include_spaces": True
    })
    print(f"  ✅ word_count result: {result.data}")


async def test_data_generation_tools(client: Client):
    """Test data generation tools."""
    print("\n🎲 Testing Data Generation Tools:")
    
    # Test number generation
    result = await client.call_tool("generate_data", {"count": 5, "data_type": "numbers"})
    print(f"  ✅ Generated 5 numbers: {result.data}")
    
    # Test name generation
    result = await client.call_tool("generate_data", {"count": 3, "data_type": "names"})
    print(f"  ✅ Generated 3 names: {result.data}")
    
    # Test user creation
    result = await client.call_tool("create_user", {
        "name": "Test User",
        "email": "<EMAIL>",
        "tags": ["demo", "test"]
    })
    print(f"  ✅ Created user: {result.data}")


async def test_system_tools(client: Client):
    """Test system information tools."""
    print("\n💻 Testing System Tools:")
    
    result = await client.call_tool("get_system_info")
    system_info = result.data
    print(f"  ✅ Platform: {system_info.get('platform', 'Unknown')}")
    print(f"  ✅ Python version: {system_info.get('python_version', 'Unknown')}")


async def test_resources(client: Client):
    """Test resource access."""
    print("\n📄 Testing Resources:")
    
    # Test server config
    try:
        resources = await client.read_resource("config://server")
        if resources:
            resource = resources[0]
            if hasattr(resource, 'text'):
                config = json.loads(resource.text)
                print(f"  ✅ Server config: {config.get('name', 'Unknown')}")
            else:
                print(f"  ✅ Got server config resource")
    except Exception as e:
        print(f"  ❌ Error reading server config: {e}")
    
    # Test health status
    try:
        resources = await client.read_resource("status://health")
        if resources:
            print(f"  ✅ Health status resource accessed")
    except Exception as e:
        print(f"  ❌ Error reading health status: {e}")
    
    # Test sample data
    for data_type in ["numbers", "colors", "countries"]:
        try:
            resources = await client.read_resource(f"data://sample/{data_type}")
            if resources:
                print(f"  ✅ Sample {data_type} data accessed")
        except Exception as e:
            print(f"  ❌ Error reading {data_type} data: {e}")
    
    # Test user profiles
    try:
        resources = await client.read_resource("users://42/profile")
        if resources:
            print(f"  ✅ User profile resource accessed")
    except Exception as e:
        print(f"  ❌ Error reading user profile: {e}")


async def test_error_handling(client: Client):
    """Test error handling scenarios."""
    print("\n❌ Testing Error Handling:")
    
    # Test invalid tool parameters
    try:
        await client.call_tool("generate_data", {"count": 1000, "data_type": "numbers"})
        print("  ❌ Should have failed for count > 100")
    except Exception:
        print("  ✅ Correctly rejected count > 100")
    
    # Test invalid math operation
    try:
        await client.call_tool("advanced_math", {"operation": "invalid", "value": 5})
        print("  ❌ Should have failed for invalid operation")
    except Exception:
        print("  ✅ Correctly rejected invalid operation")


async def test_tool_and_resource_listing(client: Client):
    """Test listing capabilities."""
    print("\n📋 Testing Listing Capabilities:")
    
    # List tools
    tools = await client.list_tools()
    print(f"  ✅ Found {len(tools)} tools:")
    for tool in tools[:5]:  # Show first 5
        print(f"    - {tool.name}: {tool.description}")
    if len(tools) > 5:
        print(f"    ... and {len(tools) - 5} more")
    
    # List resources
    resources = await client.list_resources()
    print(f"  ✅ Found {len(resources)} resources:")
    for resource in resources:
        print(f"    - {resource.uri}: {resource.description}")


async def run_comprehensive_test(transport_type: str = "stdio"):
    """Run comprehensive tests on the server."""
    print(f"🚀 FastMCP Demo Server Comprehensive Test Suite")
    print(f"Transport: {transport_type.upper()}")
    print("=" * 60)
    
    if transport_type == "stdio":
        client = Client("server.py")
    elif transport_type == "http":
        client = Client("http://127.0.0.1:8000/mcp/")
    else:
        raise ValueError(f"Unsupported transport: {transport_type}")
    
    start_time = time.time()
    
    try:
        async with client:
            print("✅ Connected to server successfully!")
            
            # Test basic connectivity
            await client.ping()
            print("✅ Ping successful!")
            
            # Run all test suites
            await test_mathematical_tools(client)
            await test_text_processing_tools(client)
            await test_data_generation_tools(client)
            await test_system_tools(client)
            await test_resources(client)
            await test_error_handling(client)
            await test_tool_and_resource_listing(client)
            
            end_time = time.time()
            print(f"\n🎉 All tests completed successfully!")
            print(f"⏱️  Total test time: {end_time - start_time:.2f} seconds")
            
    except Exception as e:
        print(f"\n💥 Test suite failed: {e}")
        if transport_type == "http":
            print("Make sure the server is running with: fastmcp run server.py --transport http")
        else:
            print("Make sure the server.py file is accessible")
        raise


async def main():
    """Main test function."""
    import sys
    
    transport = "stdio"
    if len(sys.argv) > 1:
        transport = sys.argv[1].lower()
    
    if transport not in ["stdio", "http"]:
        print("Usage: python run_comprehensive_tests.py [stdio|http]")
        sys.exit(1)
    
    await run_comprehensive_test(transport)


if __name__ == "__main__":
    asyncio.run(main())
