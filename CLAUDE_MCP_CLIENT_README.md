# Claude MCP CLI Client

A command-line interface that integrates <PERSON> API with your FastMCP server, allowing you to chat with <PERSON> while giving <PERSON> access to your MCP server tools.

## Features

- 🤖 Interactive chat with <PERSON> AI
- 🔧 <PERSON> can use your MCP server tools automatically
- 💬 Maintains conversation history
- 🛠️ Lists available MCP tools
- 🧹 Clear conversation history
- ⚙️ Configurable Claude model and server path

## Quick Start

### 1. Setup

Run the setup script to install dependencies:

```bash
python setup_claude_client.py
```

### 2. Get Claude API Key

1. Go to [<PERSON>](https://console.anthropic.com/)
2. Create an account or sign in
3. Generate an API key
4. Set it as an environment variable:

```bash
export ANTHROPIC_API_KEY=your_api_key_here
```

Or create a `.env` file:
```bash
cp .env.example .env
# Edit .env and add your API key
```

### 3. Start Your MCP Server

Make sure your FastMCP server is running:

```bash
python server.py
```

### 4. Run the Claude MCP Client

```bash
python claude_mcp_client.py
```

## Usage

### Basic Commands

- **Chat**: Just type your message and press Enter
- **Quit**: Type `quit`, `exit`, or `bye` to end the session
- **Tools**: Type `tools` to see available MCP tools
- **Clear**: Type `clear` to clear conversation history

### Command Line Options

```bash
python claude_mcp_client.py --help
```

Options:
- `--api-key`: Claude API key (or set ANTHROPIC_API_KEY env var)
- `--server`: Path to MCP server script (default: server.py)
- `--model`: Claude model to use (default: claude-3-5-sonnet-********)

### Example Session

```
🤖 Claude MCP CLI Client
==================================================
Chat with Claude! Claude can use your MCP server tools to help you.
Type 'quit', 'exit', or 'bye' to end the session.
Type 'tools' to see available MCP tools.
Type 'clear' to clear conversation history.

✅ Connected to MCP server with 12 tools available

You: What's the square root of 144?

🔧 Claude is using tool: advanced_math
Claude: The square root of 144 is 12.

You: Can you generate 5 random names for me?

🔧 Claude is using tool: generate_data
Claude: Here are 5 random names I generated for you:
1. Alice
2. Frank
3. Diana
4. Bob
5. Grace

You: tools

🔧 Available MCP Tools:
==================================================
• add: Add two numbers together.
• multiply: Multiply two numbers together.
• advanced_math: Perform advanced mathematical operations.
• reverse_text: Reverse the input text.
• word_count: Count words and characters in text.
• generate_data: Generate sample data of specified type.
• create_user: Create a new user profile.
• read_file: Read content from a file.
• write_file: Write content to a file.
• fetch_url: Fetch content from a URL.
• get_system_info: Get system information.

You: quit
👋 Goodbye!
```

## Available MCP Tools

Your FastMCP server provides these tools that Claude can use:

### Mathematical Operations
- `add`: Add two numbers
- `multiply`: Multiply two numbers  
- `advanced_math`: Square root, trigonometry, logarithms, etc.

### Text Processing
- `reverse_text`: Reverse text
- `word_count`: Count words and characters

### Data Generation
- `generate_data`: Generate random numbers, names, emails
- `create_user`: Create user profiles

### File Operations
- `read_file`: Read file contents
- `write_file`: Write to files

### Network Operations
- `fetch_url`: Fetch web content

### System Information
- `get_system_info`: Get system details

## Configuration

### Environment Variables

- `ANTHROPIC_API_KEY`: Your Claude API key (required)
- `MCP_SERVER_PATH`: Path to MCP server script (optional, default: server.py)
- `CLAUDE_MODEL`: Claude model to use (optional, default: claude-3-5-sonnet-********)

### Supported Claude Models

- `claude-3-5-sonnet-********` (default, recommended)
- `claude-3-5-haiku-********`
- `claude-3-opus-20240229`
- `claude-3-sonnet-20240229`
- `claude-3-haiku-20240307`

## Troubleshooting

### Common Issues

1. **"Failed to connect to MCP server"**
   - Make sure `python server.py` is running
   - Check that the server path is correct

2. **"Claude API key required"**
   - Set the `ANTHROPIC_API_KEY` environment variable
   - Or use `--api-key` argument

3. **"Tool call failed"**
   - Check MCP server logs for errors
   - Ensure the tool arguments are valid

4. **Import errors**
   - Run `python setup_claude_client.py` to install dependencies
   - Make sure you're using Python 3.8+

### Debug Mode

For more detailed error information, you can modify the client to enable debug logging or check the MCP server logs at `logs/fastmcp_demo.log`.

## Dependencies

- `anthropic>=0.34.0` - Claude API client
- `fastmcp>=2.0.0` - MCP client library
- `aiohttp>=3.8.0` - Async HTTP client
- `aiofiles>=23.0.0` - Async file operations
- `pydantic>=2.0.0` - Data validation

## License

This project follows the same license as your FastMCP server.
