#!/usr/bin/env python3
"""
Test suite for FastMCP Demo Server

This script provides basic tests to verify the server functionality.
Run this after starting the server to ensure everything works correctly.
"""

import asyncio
import pytest
from fastmcp import Client


class TestFastMCPDemoServer:
    """Test cases for the FastMCP demo server."""
    
    @pytest.fixture
    async def client(self):
        """Create a client connection to the server."""
        client = Client("server.py")
        async with client:
            yield client
    
    @pytest.mark.asyncio
    async def test_server_connectivity(self, client):
        """Test basic server connectivity."""
        await client.ping()
    
    @pytest.mark.asyncio
    async def test_mathematical_tools(self, client):
        """Test mathematical operation tools."""
        # Test addition
        result = await client.call_tool("add", {"a": 5, "b": 3})
        assert result == 8
        
        # Test multiplication
        result = await client.call_tool("multiply", {"a": 4, "b": 6})
        assert result == 24
        
        # Test advanced math
        result = await client.call_tool("advanced_math", {"operation": "sqrt", "value": 16})
        assert result["result"] == 4.0
        assert result["operation"] == "sqrt"
    
    @pytest.mark.asyncio
    async def test_text_processing_tools(self, client):
        """Test text processing tools."""
        # Test text reversal
        result = await client.call_tool("reverse_text", {"text": "hello"})
        assert result == "olleh"
        
        # Test word count
        result = await client.call_tool("word_count", {"text": "hello world"})
        assert result["words"] == 2
        assert result["characters"] == 11
    
    @pytest.mark.asyncio
    async def test_data_generation_tools(self, client):
        """Test data generation tools."""
        # Test number generation
        result = await client.call_tool("generate_data", {"count": 5, "data_type": "numbers"})
        assert len(result) == 5
        assert all(isinstance(x, int) for x in result)
        
        # Test name generation
        result = await client.call_tool("generate_data", {"count": 3, "data_type": "names"})
        assert len(result) == 3
        assert all(isinstance(x, str) for x in result)
    
    @pytest.mark.asyncio
    async def test_user_creation_tool(self, client):
        """Test user creation tool."""
        result = await client.call_tool("create_user", {
            "name": "Test User",
            "email": "<EMAIL>",
            "tags": ["test"]
        })
        assert result["name"] == "Test User"
        assert result["email"] == "<EMAIL>"
        assert "test" in result["tags"]
        assert isinstance(result["id"], int)
    
    @pytest.mark.asyncio
    async def test_server_resources(self, client):
        """Test server resource access."""
        # Test server config
        resources = await client.read_resource("config://server")
        assert len(resources) > 0
        config = resources[0].content
        assert "name" in config
        assert "version" in config
        
        # Test health status
        resources = await client.read_resource("status://health")
        assert len(resources) > 0
        health = resources[0].content
        assert "status" in health
    
    @pytest.mark.asyncio
    async def test_sample_data_resources(self, client):
        """Test sample data resources."""
        # Test different data types
        for data_type in ["numbers", "colors", "countries"]:
            resources = await client.read_resource(f"data://sample/{data_type}")
            assert len(resources) > 0
            data = resources[0].content
            assert isinstance(data, list)
            assert len(data) > 0
    
    @pytest.mark.asyncio
    async def test_user_profile_resources(self, client):
        """Test user profile resources."""
        resources = await client.read_resource("users://123/profile")
        assert len(resources) > 0
        profile = resources[0].content
        assert "id" in profile
        assert "name" in profile
        assert "email" in profile
        assert profile["id"] == 123
    
    @pytest.mark.asyncio
    async def test_error_handling(self, client):
        """Test error handling for invalid inputs."""
        # Test invalid math operation
        with pytest.raises(Exception):
            await client.call_tool("advanced_math", {"operation": "sqrt", "value": -1})
        
        # Test invalid data generation count
        with pytest.raises(Exception):
            await client.call_tool("generate_data", {"count": 1000, "data_type": "numbers"})
    
    @pytest.mark.asyncio
    async def test_tool_listing(self, client):
        """Test that tools can be listed."""
        tools = await client.list_tools()
        assert len(tools) > 0
        
        tool_names = [tool.name for tool in tools]
        expected_tools = ["add", "multiply", "advanced_math", "reverse_text", "word_count"]
        for expected_tool in expected_tools:
            assert expected_tool in tool_names
    
    @pytest.mark.asyncio
    async def test_resource_listing(self, client):
        """Test that resources can be listed."""
        resources = await client.list_resources()
        assert len(resources) > 0
        
        resource_uris = [resource.uri for resource in resources]
        expected_resources = ["config://server", "status://health"]
        for expected_resource in expected_resources:
            assert expected_resource in resource_uris


async def run_manual_tests():
    """Run tests manually without pytest."""
    print("🧪 Running FastMCP Demo Server Tests")
    print("=" * 50)
    
    client = Client("server.py")
    
    try:
        async with client:
            print("✅ Server connectivity test passed")
            
            # Test basic math
            result = await client.call_tool("add", {"a": 2, "b": 3})
            assert result == 5
            print("✅ Mathematical operations test passed")
            
            # Test text processing
            result = await client.call_tool("reverse_text", {"text": "test"})
            assert result == "tset"
            print("✅ Text processing test passed")
            
            # Test resources
            resources = await client.read_resource("config://server")
            assert len(resources) > 0
            print("✅ Resource access test passed")
            
            # Test tool listing
            tools = await client.list_tools()
            assert len(tools) > 0
            print("✅ Tool listing test passed")
            
            print("\n🎉 All tests passed successfully!")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise


if __name__ == "__main__":
    # Run manual tests if executed directly
    asyncio.run(run_manual_tests())
