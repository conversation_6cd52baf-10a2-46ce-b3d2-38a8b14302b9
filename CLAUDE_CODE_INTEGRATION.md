# Claude Code Integration Guide

This guide shows you how to integrate your FastMCP Demo Server with Claude Code, allowing <PERSON> to use your custom tools directly within the IDE.

## 🚀 Quick Start (Automatic Installation)

### Method 1: Using FastMCP CLI (Recommended)

1. **Run the installation script:**
   ```bash
   python install_claude_code.py
   ```

2. **Or use FastMCP CLI directly:**
   ```bash
   fastmcp install claude-code server.py --name fastmcp-demo-server
   ```

3. **Restart Claude Code** and your server will be available!

## 🔧 Manual Installation

### Method 2: Manual Configuration

If the automatic installation doesn't work, you can manually configure Claude Code:

1. **Open Claude Code Settings:**
   - Press `Cmd + ,` (Mac) or `Ctrl + ,` (Windows/Linux)
   - Or go to `Code` > `Preferences` > `Settings`

2. **Find MCP Settings:**
   - Search for "MCP" in the settings search bar
   - Or navigate to `Extensions` > `MCP`

3. **Add Server Configuration:**
   Add this configuration to your MCP settings:

   ```json
   {
     "mcpServers": {
       "fastmcp-demo-server": {
         "command": "python",
         "args": ["path/to/your/server.py"],
         "env": {
           "FASTMCP_LOG_LEVEL": "INFO"
         }
       }
     }
   }
   ```

   **Important:** Replace `path/to/your/server.py` with the actual full path to your server.py file.

### Method 3: Using Configuration File

1. **Create a configuration file** in your Claude Code settings directory:

   **Windows:**
   ```
   %APPDATA%\Claude Code\User\globalStorage\anthropic.claude-code\servers.json
   ```

   **macOS:**
   ```
   ~/Library/Application Support/Claude Code/User/globalStorage/anthropic.claude-code/servers.json
   ```

   **Linux:**
   ```
   ~/.config/Claude Code/User/globalStorage/anthropic.claude-code/servers.json
   ```

2. **Add the server configuration:**
   ```json
   {
     "fastmcp-demo-server": {
       "command": "python",
       "args": ["E:\\Code\\PythonApps\\testMCP\\server.py"],
       "env": {
         "FASTMCP_LOG_LEVEL": "INFO"
       }
     }
   }
   ```

## 🔍 Verification Steps

### 1. Check Server Status

After installation, verify your server is working:

1. **Open Claude Code**
2. **Open the Command Palette** (`Cmd/Ctrl + Shift + P`)
3. **Search for "MCP"** and look for MCP-related commands
4. **Check the Output Panel** for any MCP server logs

### 2. Test the Integration

Try these example prompts in Claude Code:

```
"Add the numbers 15 and 27"
"Generate 5 random names"
"Reverse the text 'Hello Claude'"
"Count the words in this sentence"
"Create a user profile for John Doe"
```

### 3. Check Available Tools

Ask Claude: "What tools do you have available?" and you should see your FastMCP demo server tools listed.

## 🛠️ Troubleshooting

### Common Issues and Solutions

#### 1. Server Not Starting
**Problem:** Claude Code can't start your MCP server

**Solutions:**
- Ensure Python is in your PATH
- Use full absolute paths in configuration
- Check that all dependencies are installed: `pip install -r requirements.txt`
- Verify server works standalone: `python server.py`

#### 2. Permission Issues
**Problem:** Permission denied errors

**Solutions:**
- Make sure the server.py file is executable
- Check file permissions: `chmod +x server.py` (Unix/Mac)
- Run Claude Code with appropriate permissions

#### 3. Path Issues
**Problem:** "File not found" errors

**Solutions:**
- Use absolute paths instead of relative paths
- Verify the path exists: `ls /path/to/server.py` (Unix/Mac) or `dir C:\path\to\server.py` (Windows)
- Escape backslashes in Windows paths: `"C:\\path\\to\\server.py"`

#### 4. Environment Issues
**Problem:** Import errors or missing dependencies

**Solutions:**
- Activate the correct Python environment
- Install dependencies in the same environment Claude Code uses
- Set PYTHONPATH if needed:
  ```json
  "env": {
    "PYTHONPATH": "/path/to/your/project",
    "FASTMCP_LOG_LEVEL": "INFO"
  }
  ```

### Debug Mode

Enable debug logging to troubleshoot issues:

```json
{
  "mcpServers": {
    "fastmcp-demo-server": {
      "command": "python",
      "args": ["path/to/server.py"],
      "env": {
        "FASTMCP_LOG_LEVEL": "DEBUG"
      }
    }
  }
}
```

## 📋 Example Configuration Templates

### For Windows
```json
{
  "mcpServers": {
    "fastmcp-demo-server": {
      "command": "python",
      "args": ["C:\\Code\\PythonApps\\testMCP\\server.py"],
      "env": {
        "FASTMCP_LOG_LEVEL": "INFO"
      }
    }
  }
}
```

### For macOS/Linux
```json
{
  "mcpServers": {
    "fastmcp-demo-server": {
      "command": "python3",
      "args": ["/home/<USER>/projects/testMCP/server.py"],
      "env": {
        "FASTMCP_LOG_LEVEL": "INFO"
      }
    }
  }
}
```

### With Virtual Environment
```json
{
  "mcpServers": {
    "fastmcp-demo-server": {
      "command": "/path/to/venv/bin/python",
      "args": ["/path/to/server.py"],
      "env": {
        "FASTMCP_LOG_LEVEL": "INFO"
      }
    }
  }
}
```

## 🎯 Usage Examples

Once integrated, you can use your tools naturally in Claude Code:

### Mathematical Operations
```
"Calculate the square root of 144"
"Add 25 and 17"
"What's 8 times 7?"
```

### Text Processing
```
"Reverse this text: 'FastMCP is awesome'"
"Count the words in this paragraph: [paste text]"
```

### Data Generation
```
"Generate 10 random numbers"
"Create 5 sample email addresses"
"Generate some test user data"
```

### File Operations
```
"Read the contents of config.json"
"Write this data to a file called output.txt"
```

### System Information
```
"What's the current system information?"
"Show me the Python version and platform details"
```

## 🔄 Updating the Server

When you make changes to your server:

1. **Save your changes** to server.py
2. **Restart Claude Code** (or reload the MCP servers if that option is available)
3. **Test the changes** with a simple tool call

## 📚 Additional Resources

- **FastMCP Documentation:** https://gofastmcp.com/integrations/claude-code
- **MCP Specification:** https://modelcontextprotocol.io/
- **Claude Code Documentation:** Check Claude Code's help documentation for MCP integration details

## 🎉 Success!

Once everything is set up correctly, Claude will be able to:
- ✅ Use all 11 tools from your demo server
- ✅ Access resources like server configuration and sample data
- ✅ Handle errors gracefully with your custom error handling
- ✅ Provide rich, structured responses using your Pydantic models

Your FastMCP demo server is now fully integrated with Claude Code and ready to enhance your development workflow!
