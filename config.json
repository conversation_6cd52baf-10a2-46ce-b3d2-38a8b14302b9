{"server": {"name": "FastMCP Demo Server", "version": "1.0.0", "description": "A comprehensive demonstration of FastMCP 2.0 capabilities", "author": "FastMCP Demo Team", "tags": ["demo", "educational", "comprehensive"]}, "logging": {"level": "INFO", "file": "logs/fastmcp_demo.log", "enable_colors": true, "max_file_size": "10MB", "backup_count": 5}, "security": {"max_file_size": 10485760, "max_content_length": 10485760, "max_request_timeout": 30, "max_generated_items": 100, "allowed_file_extensions": [".txt", ".json", ".csv", ".md", ".log"], "blocked_paths": ["../", "/etc/", "/var/", "C:\\Windows\\"]}, "features": {"mathematical_operations": {"enabled": true, "supported_operations": ["add", "multiply", "sqrt", "sin", "cos", "tan", "log", "exp"]}, "text_processing": {"enabled": true, "max_text_length": 50000}, "file_operations": {"enabled": true, "allowed_directories": ["./", "data/", "temp/"], "create_directories": true}, "network_operations": {"enabled": true, "allowed_protocols": ["http", "https"], "default_timeout": 10, "max_timeout": 30, "max_response_size": 1048576}, "data_generation": {"enabled": true, "max_items": 100, "supported_types": ["numbers", "names", "emails"]}}, "resources": {"server_config": {"uri": "config://server", "enabled": true, "cache_ttl": 300}, "health_status": {"uri": "status://health", "enabled": true, "cache_ttl": 60}, "sample_data": {"uri_template": "data://sample/{data_type}", "enabled": true, "supported_types": ["numbers", "colors", "countries", "fruits"]}, "user_profiles": {"uri_template": "users://{user_id}/profile", "enabled": true, "max_user_id": 9999}}, "transport": {"default": "stdio", "http": {"host": "127.0.0.1", "port": 8000, "path": "/mcp/", "cors_enabled": false, "cors_origins": ["*"]}, "sse": {"host": "127.0.0.1", "port": 8000, "sse_path": "/sse/", "message_path": "/messages/"}}, "development": {"debug_mode": false, "auto_reload": false, "inspector_enabled": false, "mock_external_services": false}, "monitoring": {"metrics_enabled": false, "health_check_interval": 60, "performance_logging": false, "error_reporting": {"enabled": true, "include_traceback": false, "mask_sensitive_data": true}}}