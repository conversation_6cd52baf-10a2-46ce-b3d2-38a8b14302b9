#!/usr/bin/env python3
"""
Setup script for Claude MCP Client

This script helps set up the Claude MCP client by installing dependencies
and providing configuration guidance.
"""

import os
import subprocess
import sys
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"stdout: {e.stdout}")
        if e.stderr:
            print(f"stderr: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8+ required, but you have {version.major}.{version.minor}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True


def install_dependencies():
    """Install required dependencies."""
    requirements_file = "claude_client_requirements.txt"
    if not Path(requirements_file).exists():
        print(f"❌ Requirements file {requirements_file} not found")
        return False
    
    return run_command(
        f"pip install -r {requirements_file}",
        "Installing Claude MCP client dependencies"
    )


def check_api_key():
    """Check if Claude API key is configured."""
    api_key = os.getenv("ANTHROPIC_API_KEY")
    if api_key:
        print("✅ ANTHROPIC_API_KEY environment variable is set")
        return True
    else:
        print("⚠️ ANTHROPIC_API_KEY environment variable is not set")
        print("You'll need to either:")
        print("  1. Set the environment variable: export ANTHROPIC_API_KEY=your_key_here")
        print("  2. Use the --api-key argument when running the client")
        return False


def create_example_env_file():
    """Create an example .env file."""
    env_content = """# Claude MCP Client Environment Variables

# Your Claude API key from https://console.anthropic.com/
ANTHROPIC_API_KEY=your_api_key_here

# Optional: Specify the MCP server path (default: server.py)
# MCP_SERVER_PATH=server.py

# Optional: Specify the Claude model (default: claude-3-5-sonnet-20241022)
# CLAUDE_MODEL=claude-3-5-sonnet-20241022
"""
    
    env_file = Path(".env.example")
    with open(env_file, "w") as f:
        f.write(env_content)
    
    print(f"✅ Created {env_file} with example configuration")
    print("Copy this to .env and fill in your API key")


def main():
    """Main setup function."""
    print("🚀 Claude MCP Client Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies. Please check the error messages above.")
        sys.exit(1)
    
    # Check API key
    api_key_configured = check_api_key()
    
    # Create example env file
    create_example_env_file()
    
    print("\n🎉 Setup completed!")
    print("\nNext steps:")
    print("1. Get your Claude API key from https://console.anthropic.com/")
    
    if not api_key_configured:
        print("2. Set your API key:")
        print("   export ANTHROPIC_API_KEY=your_key_here")
        print("   OR copy .env.example to .env and fill in your key")
    
    print("3. Make sure your MCP server is working:")
    print("   python server.py")
    print("4. Run the Claude MCP client:")
    print("   python claude_mcp_client.py")
    
    print("\nFor help, run: python claude_mcp_client.py --help")


if __name__ == "__main__":
    main()
