# 🚀 Quick Claude Code Setup

## 1-Minute Integration Guide

### Step 1: Copy Configuration
Copy this configuration to your Claude Code MCP settings:

```json
{
  "mcpServers": {
    "fastmcp-demo-server": {
      "command": "python",
      "args": ["E:\\Code\\PythonApps\\testMCP\\server.py"],
      "env": {
        "FASTMCP_LOG_LEVEL": "INFO"
      }
    }
  }
}
```

### Step 2: Access Settings
1. Open Claude Code
2. Press `Ctrl + ,` (Windows) or `Cmd + ,` (Mac)
3. Search for "MCP"
4. Paste the configuration above

### Step 3: Restart Claude Code
Close and reopen Claude Code for changes to take effect.

### Step 4: Test Integration
Try these commands in Claude Code:

- "Add 15 and 27"
- "Generate 5 random names"
- "Reverse the text 'Hello World'"
- "What tools do you have available?"

## 🛠️ Available Tools

Your FastMCP demo server provides these tools to Claude:

| Tool | Description | Example Usage |
|------|-------------|---------------|
| `add` | Add two numbers | "Add 10 and 20" |
| `multiply` | Multiply numbers | "Multiply 6 by 7" |
| `advanced_math` | Advanced math operations | "Calculate square root of 64" |
| `reverse_text` | Reverse text | "Reverse 'FastMCP'" |
| `word_count` | Count words/characters | "Count words in this text" |
| `generate_data` | Generate sample data | "Generate 5 random emails" |
| `create_user` | Create user profiles | "Create a user named John" |
| `read_file` | Read file contents | "Read the config.json file" |
| `write_file` | Write to files | "Save this data to output.txt" |
| `fetch_url` | Fetch web content | "Get content from example.com" |
| `get_system_info` | System information | "Show system details" |

## 🔍 Troubleshooting

**Server not working?**
1. Check the path in the configuration is correct
2. Ensure Python is installed and in PATH
3. Verify dependencies: `pip install -r requirements.txt`
4. Test server manually: `python server.py`

**Need help?**
- Check the full guide: `CLAUDE_CODE_INTEGRATION.md`
- Run diagnostics: `python run_comprehensive_tests.py`

## ✅ Success Indicators

You'll know it's working when:
- Claude responds to tool requests
- No error messages in Claude Code
- Tools appear when you ask "What tools do you have?"

🎉 **Enjoy your enhanced Claude Code experience with custom FastMCP tools!**
