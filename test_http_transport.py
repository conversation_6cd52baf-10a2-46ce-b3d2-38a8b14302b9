#!/usr/bin/env python3
"""
Test HTTP transport for FastMCP Demo Server

This script tests the server running with HTTP transport.
"""

import asyncio
import json
import time
from fastmcp import Client


async def test_http_server():
    """Test the server running with HTTP transport."""
    print("Testing FastMCP Demo Server with HTTP transport...")
    
    # Connect to HTTP server
    client = Client("http://127.0.0.1:8000/mcp/")
    
    try:
        async with client:
            print("✅ Connected to HTTP server successfully!")
            
            # Test basic connectivity
            await client.ping()
            print("✅ Ping successful!")
            
            # Test a simple tool
            result = await client.call_tool("add", {"a": 10, "b": 20})
            print(f"✅ Tool test: 10 + 20 = {result}")
            
            # Test resource access
            resources = await client.read_resource("config://server")
            if resources:
                resource = resources[0]
                if hasattr(resource, 'text'):
                    config = json.loads(resource.text)
                    print(f"✅ Resource test: Server name = {config.get('name', 'Unknown')}")
                else:
                    print(f"✅ Resource test: Got resource of type {type(resource)}")
            
            print("🎉 All HTTP transport tests passed!")
            
    except Exception as e:
        print(f"❌ HTTP transport test failed: {e}")
        print("Make sure the server is running with: fastmcp run server.py --transport http")


if __name__ == "__main__":
    asyncio.run(test_http_server())
