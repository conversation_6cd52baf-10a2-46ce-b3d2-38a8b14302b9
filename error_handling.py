"""
Enhanced error handling for FastMCP Demo Server

Provides custom exceptions, error decorators, and validation utilities
to improve error reporting and debugging.
"""

import functools
import traceback
from typing import Any, Callable, Dict, Optional, TypeVar, Union

from fastmcp.exceptions import ToolError
from logging_config import default_logger

# Type variable for decorated functions
F = TypeVar('F', bound=Callable[..., Any])


class ValidationError(ToolError):
    """Raised when input validation fails"""
    pass


class FileOperationError(ToolError):
    """Raised when file operations fail"""
    pass


class NetworkError(ToolError):
    """Raised when network operations fail"""
    pass


class DataProcessingError(ToolError):
    """Raised when data processing fails"""
    pass


def handle_tool_errors(func: F) -> F:
    """
    Decorator to handle common tool errors and provide better error messages.
    
    This decorator catches common exceptions and converts them to appropriate
    ToolError subclasses with user-friendly messages.
    """
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ToolError:
            # Re-raise ToolErrors as-is
            raise
        except ValueError as e:
            default_logger.error(f"Validation error in {func.__name__}: {str(e)}")
            raise ValidationError(f"Invalid input: {str(e)}")
        except FileNotFoundError as e:
            default_logger.error(f"File not found in {func.__name__}: {str(e)}")
            raise FileOperationError(f"File not found: {str(e)}")
        except PermissionError as e:
            default_logger.error(f"Permission error in {func.__name__}: {str(e)}")
            raise FileOperationError(f"Permission denied: {str(e)}")
        except ConnectionError as e:
            default_logger.error(f"Network error in {func.__name__}: {str(e)}")
            raise NetworkError(f"Network connection failed: {str(e)}")
        except Exception as e:
            default_logger.error(f"Unexpected error in {func.__name__}: {str(e)}")
            default_logger.error(f"Traceback: {traceback.format_exc()}")
            raise ToolError(f"Unexpected error: {str(e)}")
    
    return wrapper


def handle_async_tool_errors(func: F) -> F:
    """
    Async version of the error handling decorator.
    """
    
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except ToolError:
            # Re-raise ToolErrors as-is
            raise
        except ValueError as e:
            default_logger.error(f"Validation error in {func.__name__}: {str(e)}")
            raise ValidationError(f"Invalid input: {str(e)}")
        except FileNotFoundError as e:
            default_logger.error(f"File not found in {func.__name__}: {str(e)}")
            raise FileOperationError(f"File not found: {str(e)}")
        except PermissionError as e:
            default_logger.error(f"Permission error in {func.__name__}: {str(e)}")
            raise FileOperationError(f"Permission denied: {str(e)}")
        except ConnectionError as e:
            default_logger.error(f"Network error in {func.__name__}: {str(e)}")
            raise NetworkError(f"Network connection failed: {str(e)}")
        except Exception as e:
            default_logger.error(f"Unexpected error in {func.__name__}: {str(e)}")
            default_logger.error(f"Traceback: {traceback.format_exc()}")
            raise ToolError(f"Unexpected error: {str(e)}")
    
    return wrapper


def validate_numeric_range(
    value: Union[int, float],
    min_value: Optional[Union[int, float]] = None,
    max_value: Optional[Union[int, float]] = None,
    field_name: str = "value"
) -> None:
    """
    Validate that a numeric value is within the specified range.
    
    Args:
        value: The value to validate
        min_value: Minimum allowed value (inclusive)
        max_value: Maximum allowed value (inclusive)
        field_name: Name of the field for error messages
    
    Raises:
        ValidationError: If the value is outside the allowed range
    """
    if min_value is not None and value < min_value:
        raise ValidationError(f"{field_name} must be at least {min_value}, got {value}")
    
    if max_value is not None and value > max_value:
        raise ValidationError(f"{field_name} must be at most {max_value}, got {value}")


def validate_string_length(
    value: str,
    min_length: Optional[int] = None,
    max_length: Optional[int] = None,
    field_name: str = "string"
) -> None:
    """
    Validate that a string length is within the specified range.
    
    Args:
        value: The string to validate
        min_length: Minimum allowed length
        max_length: Maximum allowed length
        field_name: Name of the field for error messages
    
    Raises:
        ValidationError: If the string length is outside the allowed range
    """
    length = len(value)
    
    if min_length is not None and length < min_length:
        raise ValidationError(f"{field_name} must be at least {min_length} characters, got {length}")
    
    if max_length is not None and length > max_length:
        raise ValidationError(f"{field_name} must be at most {max_length} characters, got {length}")


def validate_file_path(file_path: str, must_exist: bool = False) -> None:
    """
    Validate a file path.
    
    Args:
        file_path: The file path to validate
        must_exist: Whether the file must already exist
    
    Raises:
        ValidationError: If the file path is invalid
        FileOperationError: If the file doesn't exist when required
    """
    if not file_path or not isinstance(file_path, str):
        raise ValidationError("File path must be a non-empty string")
    
    # Check for potentially dangerous paths
    if ".." in file_path or file_path.startswith("/"):
        raise ValidationError("File path contains potentially unsafe components")
    
    if must_exist:
        from pathlib import Path
        if not Path(file_path).exists():
            raise FileOperationError(f"File does not exist: {file_path}")


def safe_json_serialize(data: Any) -> str:
    """
    Safely serialize data to JSON with error handling.
    
    Args:
        data: The data to serialize
    
    Returns:
        JSON string representation
    
    Raises:
        DataProcessingError: If serialization fails
    """
    import json
    
    try:
        return json.dumps(data, default=str, indent=2)
    except (TypeError, ValueError) as e:
        raise DataProcessingError(f"Failed to serialize data to JSON: {str(e)}")


def create_error_response(error: Exception, include_traceback: bool = False) -> Dict[str, Any]:
    """
    Create a standardized error response dictionary.
    
    Args:
        error: The exception that occurred
        include_traceback: Whether to include the full traceback
    
    Returns:
        Dictionary containing error information
    """
    response = {
        "error_type": type(error).__name__,
        "error_message": str(error),
        "timestamp": str(default_logger.handlers[0].formatter.formatTime(None))
    }
    
    if include_traceback:
        response["traceback"] = traceback.format_exc()
    
    return response
